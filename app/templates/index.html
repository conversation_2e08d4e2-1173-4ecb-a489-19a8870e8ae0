<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Application</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/react/18.2.0/umd/react.production.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/react-dom/18.2.0/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Updated Lucide import -->
    <script src="https://unpkg.com/@lucide/lucide@latest"></script>
    <!-- Add marked.js for Markdown rendering -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- Add GitHub Markdown CSS -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.1.0/github-markdown.min.css">
    <style>
        /* Add some basic padding and apply markdown styles to a container */
        .markdown-body {
            box-sizing: border-box;
            min-width: 200px;
            max-width: 980px;
            margin: 0 auto;
            padding: 45px;
        }

        @media (max-width: 767px) {
            .markdown-body {
                padding: 15px;
            }
        }
    </style>
</head>

<body class="m-0 p-0 h-screen flex flex-col overflow-hidden">
    <!-- Top bar with user info -->
    {% if user %}
    <header class="bg-white border-b border-gray-200 flex-shrink-0">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3 flex items-center justify-between">
            <div class="flex items-center space-x-3">
                {% if user.picture %}
                <img src="{{ user.picture }}" alt="{{ user.name or user.email }}" class="h-8 w-8 rounded-full border border-gray-200" />
                {% endif %}
                <span class="text-sm text-gray-700">{{ user.name or user.email }}</span>
            </div>
            <a href="/logout" class="text-sm text-blue-600 hover:underline">Sign out</a>
        </div>
    </header>
    {% endif %}

    <div id="root" class="flex-1 min-h-0"></div>
    <script src="/static/js/chat.js" type="text/javascript"></script>
</body>

</html>