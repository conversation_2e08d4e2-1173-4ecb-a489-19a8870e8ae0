from app.store.db import DB

class MessageStore:
    async def save_message(self, user_id, message_id, parent_id, prompt, response: str):
        db = DB()
        try:
            await db.connect()
            await db.conn.execute('''
INSERT INTO public.messages
(user_id, provider_message_id, parent_message_id, prompt, response)
VALUES($1, $2, $3, $4, $5);''', user_id, message_id, parent_id, prompt, response)
            await db.disconnect()
        except Exception as e:
            print(f"Error saving message: {e}")
        finally:
            await db.disconnect()