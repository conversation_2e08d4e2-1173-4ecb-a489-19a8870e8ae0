import os

import asyncpg

from app.core.config import settings


class DB:
    def __init__(self):
        self.conn = None

    async def connect(self) -> None:
        try:
            self.conn = await asyncpg.connect(
                host="*************",
                port=15432,
                database="gooda",
                user=settings.POSTGRES_USER,
                password=settings.POSTGRES_PASSWORD
            )
            print("Successfully connected to Gooda database")
        except Exception as e:
            print(f"Error connecting to database: {e}")

    async def disconnect(self) -> None:
        if self.conn:
            await self.conn.close()
            print("Database connection closed")